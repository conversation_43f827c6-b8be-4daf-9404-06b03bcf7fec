<template>
  <div class="dropdown dropdown-end">
    <!-- <PERSON> -->
    <div 
      tabindex="0" 
      role="button" 
      class="btn btn-ghost btn-circle relative"
      :class="{ 'animate-pulse': hasUnread }"
    >
      <!-- Bell Icon -->
      <svg 
        class="w-6 h-6" 
        :class="hasUnread ? 'text-warning' : 'text-base-content'"
        fill="none" 
        stroke="currentColor" 
        viewBox="0 0 24 24"
      >
        <path 
          stroke-linecap="round" 
          stroke-linejoin="round" 
          stroke-width="2" 
          d="M9.5 19C8.89555 19 7.01237 19 5.61714 19C4.87375 19 4.39116 18.2177 4.72361 17.5528L5.57771 15.8446C5.85542 15.2892 6 14.6774 6 14.0564C6 13.2867 6 12.1434 6 11C6 9 7 5 12 5C17 5 18 9 18 11C18 12.1434 18 13.2867 18 14.0564C18 14.6774 18.1446 15.2892 18.4223 15.8446L19.2764 17.5528C19.6088 18.2177 19.1253 19 18.382 19H14.5M9.5 19C9.5 21 10.5 22 12 22C13.5 22 14.5 21 14.5 19M9.5 19C11.0621 19 14.5 19 14.5 19M12 5V3" 
        />
      </svg>
      
      <!-- Notification Badge -->
      <div 
        v-if="unreadCount > 0" 
        class="badge badge-error badge-sm absolute -top-1 -right-1"
      >
        {{ unreadCount > 99 ? '99+' : unreadCount }}
      </div>
    </div>

    <!-- Dropdown Content -->
    <div
      tabindex="0"
      class="dropdown-content bg-base-100 rounded-box z-[1] mt-3 p-0 shadow-xl border border-base-300 w-80 max-w-[calc(100vw-1rem)] -translate-x-[calc(100%-2.5rem)] sm:translate-x-0 sm:w-80"
    >
      <!-- Header -->
      <div class="flex items-center justify-between p-4 border-b border-base-300">
        <h3 class="font-semibold text-lg">Notifications</h3>
        <button 
          v-if="hasUnread"
          @click="handleMarkAllAsRead"
          class="btn btn-sm btn-ghost"
          :disabled="loading"
        >
          Mark all read
        </button>
      </div>

      <!-- Loading State -->
      <div v-if="loading && notifications.length === 0" class="p-8 text-center">
        <div class="loading loading-spinner loading-md"></div>
        <p class="text-sm text-base-content/70 mt-2">Loading notifications...</p>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="p-8 text-center">
        <svg class="w-12 h-12 mx-auto text-error mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <p class="text-sm text-error">{{ error }}</p>
        <button @click="fetchNotifications()" class="btn btn-sm btn-outline mt-2">
          Try again
        </button>
      </div>

      <!-- Notifications List -->
      <div v-else-if="notifications.length > 0" class="max-h-80 overflow-y-auto">
        <div 
          v-for="notification in notifications" 
          :key="notification.id"
          class="notification-item p-4 border-b border-base-200 hover:bg-base-50 transition-colors cursor-pointer"
          :class="{ 'bg-info/5': !notification.isRead }"
          @click="handleNotificationClick(notification)"
        >
          <div class="flex items-start gap-3">
            <!-- Icon -->
            <div 
              class="flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center"
              :class="getIconBackgroundClass(notification)"
            >
              <svg 
                class="w-4 h-4" 
                :class="getPriorityColorClass(notification.priority)"
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path 
                  stroke-linecap="round" 
                  stroke-linejoin="round" 
                  stroke-width="2" 
                  :d="getNotificationIcon(notification)" 
                />
              </svg>
            </div>

            <!-- Content -->
            <div class="flex-1 min-w-0">
              <div class="flex items-start justify-between">
                <h4 
                  class="text-sm font-medium truncate"
                  :class="notification.isRead ? 'text-base-content/70' : 'text-base-content'"
                >
                  {{ notification.title }}
                </h4>
                <div class="flex items-center gap-1 ml-2">
                  <!-- Unread Indicator -->
                  <div 
                    v-if="!notification.isRead" 
                    class="w-2 h-2 bg-primary rounded-full flex-shrink-0"
                  ></div>
                  <!-- Time -->
                  <span class="text-xs text-base-content/50 flex-shrink-0">
                    {{ formatRelativeTime(notification.createdAt) }}
                  </span>
                </div>
              </div>
              
              <p 
                class="text-sm mt-1 line-clamp-2"
                :class="notification.isRead ? 'text-base-content/60' : 'text-base-content/80'"
              >
                {{ notification.message }}
              </p>

              <!-- Action Button -->
              <div v-if="notification.actionUrl && notification.actionText" class="mt-2">
                <button 
                  @click.stop="handleActionClick(notification)"
                  class="btn btn-xs btn-primary"
                >
                  {{ notification.actionText }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-else class="p-8 text-center">
        <svg class="w-12 h-12 mx-auto text-base-content/30 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path 
            stroke-linecap="round" 
            stroke-linejoin="round" 
            stroke-width="2" 
            d="M9.5 19C8.89555 19 7.01237 19 5.61714 19C4.87375 19 4.39116 18.2177 4.72361 17.5528L5.57771 15.8446C5.85542 15.2892 6 14.6774 6 14.0564C6 13.2867 6 12.1434 6 11C6 9 7 5 12 5C17 5 18 9 18 11C18 12.1434 18 13.2867 18 14.0564C18 14.6774 18.1446 15.2892 18.4223 15.8446L19.2764 17.5528C19.6088 18.2177 19.1253 19 18.382 19H14.5M9.5 19C9.5 21 10.5 22 12 22C13.5 22 14.5 21 14.5 19M9.5 19C11.0621 19 14.5 19 14.5 19M12 5V3" 
          />
        </svg>
        <p class="text-sm text-base-content/60">No notifications yet</p>
        <p class="text-xs text-base-content/40 mt-1">You'll see important updates here</p>
      </div>

      <!-- Footer -->
      <div v-if="notifications.length > 0" class="p-3 border-t border-base-300">
        <button 
          @click="viewAllNotifications"
          class="btn btn-sm btn-ghost w-full"
        >
          View all notifications
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useNotifications, type Notification } from '../../composables/useNotifications'
import { useToast } from '../../composables/useToast'

// Composables
const router = useRouter()
const { 
  notifications, 
  unreadCount, 
  loading, 
  error, 
  hasUnread,
  fetchNotifications,
  markAsRead,
  markAllAsRead,
  getNotificationIcon,
  getPriorityColorClass,
  formatRelativeTime,
  initializeNotifications
} = useNotifications()
const { success, error: showError } = useToast()

// Local state
const isDropdownOpen = ref(false)

/**
 * Handle notification click - mark as read and navigate if needed
 */
const handleNotificationClick = async (notification: Notification): Promise<void> => {
  try {
    // Mark as read if unread
    if (!notification.isRead) {
      await markAsRead(notification.id)
    }

    // Navigate to action URL if available
    if (notification.actionUrl) {
      if (notification.actionUrl.startsWith('/')) {
        router.push(notification.actionUrl)
      } else {
        window.open(notification.actionUrl, '_blank')
      }
    }

    // Close dropdown
    isDropdownOpen.value = false
  } catch (err) {
    showError('Failed to mark notification as read')
  }
}

/**
 * Handle action button click
 */
const handleActionClick = async (notification: Notification): Promise<void> => {
  try {
    // Mark as read if unread
    if (!notification.isRead) {
      await markAsRead(notification.id)
    }

    // Navigate to action URL
    if (notification.actionUrl) {
      if (notification.actionUrl.startsWith('/')) {
        router.push(notification.actionUrl)
      } else {
        window.open(notification.actionUrl, '_blank')
      }
    }

    // Close dropdown
    isDropdownOpen.value = false
  } catch (err) {
    showError('Failed to mark notification as read')
  }
}

/**
 * Handle mark all as read
 */
const handleMarkAllAsRead = async (): Promise<void> => {
  try {
    await markAllAsRead()
    success('All notifications marked as read')
  } catch (err) {
    showError('Failed to mark notifications as read')
  }
}

/**
 * Navigate to full notifications page (if it exists)
 */
const viewAllNotifications = (): void => {
  router.push('/notifications')
  isDropdownOpen.value = false
}

/**
 * Get background class for notification icon
 */
const getIconBackgroundClass = (notification: Notification): string => {
  if (!notification.isRead) {
    switch (notification.priority) {
      case 'URGENT':
        return 'bg-error/10'
      case 'HIGH':
        return 'bg-warning/10'
      case 'MEDIUM':
        return 'bg-info/10'
      default:
        return 'bg-base-200'
    }
  }
  return 'bg-base-200'
}

/**
 * Load notifications when dropdown opens
 */
const loadNotifications = async (): Promise<void> => {
  if (notifications.value.length === 0) {
    await fetchNotifications({ limit: 10 })
  }
}

// Initialize notifications on mount
onMounted(() => {
  initializeNotifications()
})
</script>

<style scoped>
/* Line clamp utility */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Smooth hover transitions */
.notification-item {
  transition: background-color 0.2s ease;
}

/* Custom scrollbar for notifications list */
.max-h-80::-webkit-scrollbar {
  width: 4px;
}

.max-h-80::-webkit-scrollbar-track {
  background: transparent;
}

.max-h-80::-webkit-scrollbar-thumb {
  background: hsl(var(--bc) / 0.2);
  border-radius: 2px;
}

.max-h-80::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--bc) / 0.3);
}
</style>
