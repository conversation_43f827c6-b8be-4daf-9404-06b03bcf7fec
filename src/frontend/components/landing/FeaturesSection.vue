<template>
  <div id="features" class="bg-base-200 py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Section Header -->
      <div class="text-center mb-16">
        <div class="badge badge-primary mb-4">Features</div>
        <h2 class="text-3xl lg:text-4xl font-bold text-base-content mb-4">
          Everything you need for email automation
        </h2>
        <p class="text-xl text-base-content/70 max-w-3xl mx-auto">
          Built for developers and automation enthusiasts who need reliable email processing 
          with seamless integration capabilities.
        </p>
      </div>
      
      <!-- Features Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <!-- GDPR Compliance -->
        <div class="card bg-base-100 shadow-lg hover:shadow-xl transition-shadow">
          <div class="card-body">
            <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
              <svg class="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
              </svg>
            </div>
            <h3 class="card-title text-lg mb-2">EU-sovereign</h3>
            <p class="text-base-content/70">
              EU-based infrastructure, owned and operated. Automatic data expiration after 30 days. Privacy-first by design, performance uncompromised.
            </p>
          </div>
        </div>
        
        <!-- Real-time Processing -->
        <div class="card bg-base-100 shadow-lg hover:shadow-xl transition-shadow">
          <div class="card-body">
            <div class="w-12 h-12 bg-secondary/10 rounded-lg flex items-center justify-center mb-4">
              <svg class="w-6 h-6 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
              </svg>
            </div>
            <h3 class="card-title text-lg mb-2">Automate</h3>
            <p class="text-base-content/70">
              Advanced email parsing with instant webhook delivery. Built-in queueing and automatic retries ensure nothing gets lost.
            </p>
          </div>
        </div>
        
        <!-- Multi-domain Support -->
        <div class="card bg-base-100 shadow-lg hover:shadow-xl transition-shadow">
          <div class="card-body">
            <div class="w-12 h-12 bg-accent/10 rounded-lg flex items-center justify-center mb-4">
              <svg class="w-6 h-6 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"/>
              </svg>
            </div>
            <h3 class="card-title text-lg mb-2">Connect</h3>
            <p class="text-base-content/70">
              Integrates with n8n, Zapier, Make.com, and any webhook-enabled tool. Includes DNS verification and unlimited domains & aliases.
            </p>
          </div>
        </div>
        
        <!-- Developer API -->
        <div class="card bg-base-100 shadow-lg hover:shadow-xl transition-shadow">
          <div class="card-body">
            <div class="w-12 h-12 bg-info/10 rounded-lg flex items-center justify-center mb-4">
              <svg class="w-6 h-6 text-info" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"/>
              </svg>
            </div>
            <h3 class="card-title text-lg mb-2">Developer API</h3>
            <p class="text-base-content/70">
              REST API for managing domains, aliases, webhooks, and delivery metrics. Fully documented with OpenAPI spec.
            </p>
          </div>
        </div>
        
        <!-- Email Parsing -->
        <div class="card bg-base-100 shadow-lg hover:shadow-xl transition-shadow">
          <div class="card-body">
            <div class="w-12 h-12 bg-success/10 rounded-lg flex items-center justify-center mb-4">
              <svg class="w-6 h-6 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
              </svg>
            </div>
            <h3 class="card-title text-lg mb-2">Full email parsing</h3>
            <p class="text-base-content/70">
              Extract headers, HTML/plain content, and attachments (roadmap). Compatible with all standard formats and encodings.
            </p>
          </div>
        </div>
        
        <!-- Attachment storage -->
        <div class="card bg-base-100 shadow-lg hover:shadow-xl transition-shadow">
          <div class="card-body">
            <div class="w-12 h-12 bg-warning/10 rounded-lg flex items-center justify-center mb-4">
              <!-- Paperclip SVG-->
              <svg class="w-6 h-6 text-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24"><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <title>attachment_line</title> <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"> <g id="File" transform="translate(-48.000000, 0.000000)"> <g id="attachment_line" transform="translate(48.000000, 0.000000)"> <path d="M24,0 L24,24 L0,24 L0,0 L24,0 Z M12.5934901,23.257841 L12.5819402,23.2595131 L12.5108777,23.2950439 L12.4918791,23.2987469 L12.4918791,23.2987469 L12.4767152,23.2950439 L12.4056548,23.2595131 C12.3958229,23.2563662 12.3870493,23.2590235 12.3821421,23.2649074 L12.3780323,23.275831 L12.360941,23.7031097 L12.3658947,23.7234994 L12.3769048,23.7357139 L12.4804777,23.8096931 L12.4953491,23.8136134 L12.4953491,23.8136134 L12.5071152,23.8096931 L12.6106902,23.7357139 L12.6232938,23.7196733 L12.6232938,23.7196733 L12.6266527,23.7031097 L12.609561,23.275831 C12.6075724,23.2657013 12.6010112,23.2592993 12.5934901,23.257841 L12.5934901,23.257841 Z M12.8583906,23.1452862 L12.8445485,23.1473072 L12.6598443,23.2396597 L12.6498822,23.2499052 L12.6498822,23.2499052 L12.6471943,23.2611114 L12.6650943,23.6906389 L12.6699349,23.7034178 L12.6699349,23.7034178 L12.678386,23.7104931 L12.8793402,23.8032389 C12.8914285,23.8068999 12.9022333,23.8029875 12.9078286,23.7952264 L12.9118235,23.7811639 L12.8776777,23.1665331 C12.8752882,23.1545897 12.8674102,23.1470016 12.8583906,23.1452862 L12.8583906,23.1452862 Z M12.1430473,23.1473072 C12.1332178,23.1423925 12.1221763,23.1452606 12.1156365,23.1525954 L12.1099173,23.1665331 L12.0757714,23.7811639 C12.0751323,23.7926639 12.0828099,23.8018602 12.0926481,23.8045676 L12.108256,23.8032389 L12.3092106,23.7104931 L12.3186497,23.7024347 L12.3186497,23.7024347 L12.3225043,23.6906389 L12.340401,23.2611114 L12.337245,23.2485176 L12.337245,23.2485176 L12.3277531,23.2396597 L12.1430473,23.1473072 Z" id="MingCute" fill-rule="nonzero"> </path> <path d="M18.7103,17.5652 C20.37,15.9055 20.37,13.2145 18.7103,11.5548 L12.1696,5.01406 C11.7791,4.62353 11.7791,3.99037 12.1696,3.59984 C12.5601,3.20932 13.1933,3.20932 13.5838,3.59984 L20.1245,10.1406 C22.5653,12.5814 22.5653,16.5386 20.1245,18.9794 C17.6838,21.4202 13.7265,21.4202 11.2857,18.9794 L3.33178,11.0255 C1.57385,9.26757 1.57385,6.4174 3.33178,4.65947 C5.08971,2.90154 7.93988,2.90154 9.69781,4.65947 L17.6507,12.6123 C18.7252,13.6869 18.7252,15.429 17.6507,16.5035 C16.5762,17.578 14.834,17.578 13.7595,16.5035 L6.51272,9.2567 C6.1222,8.86617 6.1222,8.23301 6.51272,7.84248 C6.90325,7.45196 7.53641,7.45196 7.92694,7.84248 L15.1737,15.0893 C15.4672,15.3828 15.943,15.3828 16.2365,15.0893 C16.5299,14.7958 16.5299,14.32 16.2365,14.0266 L8.2836,6.07368 C7.30671,5.0968 5.72287,5.0968 4.74599,6.07368 C3.76911,7.05056 3.76911,8.6344 4.74599,9.61129 L12.6999,17.5652 C14.3596,19.2249 17.0506,19.2249 18.7103,17.5652 Z" fill="currentColor"> </path> </g> </g> </g> </g></svg>
            </div>
            <div class="flex items-center gap-2 mb-2">
              <h3 class="card-title text-lg">Attachment handling</h3>
            </div>
            <p class="text-base-content/70">
              Receive attachments as base64-encoded data in the webhook payload or stored in S3-compatible storage.
            </p>
          </div>
        </div>
      </div>
      
      <!-- Bottom CTA -->
      <div class="text-center mt-16">
        <div class="bg-base-100 rounded-2xl p-8 shadow-lg inline-block">
          <h3 class="text-xl font-semibold mb-4">Ready to get started?</h3>
          <p class="text-base-content/70 mb-6">
            Set up your first domain in under 5 minutes
          </p>
          <router-link to="/register" class="btn btn-primary">
            Get started free
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Features section showcasing key capabilities
</script>
