import { logger } from '../utils/logger.js';

export interface SitemapUrl {
  loc: string;
  lastmod?: string;
  changefreq?: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
  priority?: number;
}

export class SitemapService {
  private baseUrl: string;

  constructor(baseUrl: string = 'https://emailconnect.eu') {
    this.baseUrl = baseUrl.replace(/\/$/, ''); // Remove trailing slash
  }

  /**
   * Generate sitemap XML
   */
  generateSitemap(): string {
    const urls = this.getStaticUrls();
    
    const urlElements = urls.map(url => {
      let urlXml = `    <url>\n      <loc>${url.loc}</loc>\n`;
      
      if (url.lastmod) {
        urlXml += `      <lastmod>${url.lastmod}</lastmod>\n`;
      }
      
      if (url.changefreq) {
        urlXml += `      <changefreq>${url.changefreq}</changefreq>\n`;
      }
      
      if (url.priority !== undefined) {
        urlXml += `      <priority>${url.priority.toFixed(1)}</priority>\n`;
      }
      
      urlXml += `    </url>`;
      return urlXml;
    }).join('\n');

    return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${urlElements}
</urlset>`;
  }

  /**
   * Get static URLs for the sitemap
   */
  private getStaticUrls(): SitemapUrl[] {
    const now = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
    
    return [
      // Homepage
      {
        loc: this.baseUrl,
        lastmod: now,
        changefreq: 'weekly',
        priority: 1.0
      },
      
      // Authentication pages
      {
        loc: `${this.baseUrl}/login`,
        lastmod: now,
        changefreq: 'monthly',
        priority: 0.8
      },
      {
        loc: `${this.baseUrl}/register`,
        lastmod: now,
        changefreq: 'monthly',
        priority: 0.8
      },
      
      // Main app sections (for SEO, though they require auth)
      {
        loc: `${this.baseUrl}/dashboard`,
        lastmod: now,
        changefreq: 'weekly',
        priority: 0.9
      },
      {
        loc: `${this.baseUrl}/domains`,
        lastmod: now,
        changefreq: 'weekly',
        priority: 0.9
      },
      {
        loc: `${this.baseUrl}/aliases`,
        lastmod: now,
        changefreq: 'weekly',
        priority: 0.9
      },
      {
        loc: `${this.baseUrl}/webhooks`,
        lastmod: now,
        changefreq: 'weekly',
        priority: 0.9
      },
      {
        loc: `${this.baseUrl}/logs`,
        lastmod: now,
        changefreq: 'daily',
        priority: 0.7
      },
      {
        loc: `${this.baseUrl}/settings`,
        lastmod: now,
        changefreq: 'monthly',
        priority: 0.6
      },
      
      // API Documentation
      {
        loc: `${this.baseUrl}/docs`,
        lastmod: now,
        changefreq: 'weekly',
        priority: 0.8
      },
      
      // Future public pages (when implemented)
      {
        loc: `${this.baseUrl}/pricing`,
        lastmod: now,
        changefreq: 'monthly',
        priority: 0.8
      },
      {
        loc: `${this.baseUrl}/features`,
        lastmod: now,
        changefreq: 'monthly',
        priority: 0.7
      },
      {
        loc: `${this.baseUrl}/help`,
        lastmod: now,
        changefreq: 'weekly',
        priority: 0.6
      },
      {
        loc: `${this.baseUrl}/changelog`,
        lastmod: now,
        changefreq: 'weekly',
        priority: 0.5
      },
      
      // Legal pages (when implemented)
      {
        loc: `${this.baseUrl}/privacy`,
        lastmod: now,
        changefreq: 'yearly',
        priority: 0.3
      },
      {
        loc: `${this.baseUrl}/terms`,
        lastmod: now,
        changefreq: 'yearly',
        priority: 0.3
      }
    ];
  }

  /**
   * Get dynamic URLs (for future expansion)
   * This could include blog posts, help articles, etc.
   */
  private async getDynamicUrls(): Promise<SitemapUrl[]> {
    try {
      // Future: Add dynamic content like blog posts, help articles, etc.
      // For now, return empty array
      return [];
    } catch (error: any) {
      logger.error({ error: error.message }, 'Failed to get dynamic URLs for sitemap');
      return [];
    }
  }

  /**
   * Generate sitemap with both static and dynamic URLs
   */
  async generateFullSitemap(): Promise<string> {
    try {
      const staticUrls = this.getStaticUrls();
      const dynamicUrls = await this.getDynamicUrls();
      
      const allUrls = [...staticUrls, ...dynamicUrls];
      
      const urlElements = allUrls.map(url => {
        let urlXml = `    <url>\n      <loc>${url.loc}</loc>\n`;
        
        if (url.lastmod) {
          urlXml += `      <lastmod>${url.lastmod}</lastmod>\n`;
        }
        
        if (url.changefreq) {
          urlXml += `      <changefreq>${url.changefreq}</changefreq>\n`;
        }
        
        if (url.priority !== undefined) {
          urlXml += `      <priority>${url.priority.toFixed(1)}</priority>\n`;
        }
        
        urlXml += `    </url>`;
        return urlXml;
      }).join('\n');

      return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${urlElements}
</urlset>`;
    } catch (error: any) {
      logger.error({ error: error.message }, 'Failed to generate full sitemap');
      // Fallback to static sitemap
      return this.generateSitemap();
    }
  }
}
