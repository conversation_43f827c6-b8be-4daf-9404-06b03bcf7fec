#!/bin/bash

# Content-only deployment script
# Usage: ./deploy/update-content.sh [--force]

set -e

echo "📝 Content-Only Deployment Script"
echo "=================================="

# Configuration
DEPLOY_DIR="/opt/eu-email-webhook"
API_URL="http://localhost:3000"

# Check if we're in the right directory
if [ ! -f "docker-compose.prod.yml" ]; then
    echo "❌ Error: Must be run from the application root directory"
    echo "   Expected to find docker-compose.prod.yml"
    exit 1
fi

# Detect Docker Compose version
if docker compose version &>/dev/null; then
    DOCKER_COMPOSE="docker compose"
elif command -v docker-compose &>/dev/null; then
    DOCKER_COMPOSE="docker-compose"
else
    echo "❌ No supported Docker Compose found"
    exit 1
fi

# Check if force flag is provided
FORCE_UPDATE=false
if [ "$1" = "--force" ]; then
    FORCE_UPDATE=true
    echo "🔄 Force update enabled"
fi

# Check for content changes (unless force update)
if [ "$FORCE_UPDATE" = false ]; then
    echo "🔍 Checking for content changes..."
    git fetch origin
    
    # Check if there are content changes
    CONTENT_CHANGES=$(git diff HEAD origin/main --name-only | grep "^content/" || true)
    
    if [ -z "$CONTENT_CHANGES" ]; then
        echo "ℹ️  No content changes detected"
        echo "   Use --force to update anyway"
        exit 0
    fi
    
    echo "📝 Content changes detected:"
    echo "$CONTENT_CHANGES"
fi

# Update content directory
echo "📁 Updating content directory..."
git checkout origin/main -- content/

# Clear content cache
echo "🔄 Clearing content cache..."
if curl -f --max-time 10 -X POST "$API_URL/api/admin/cache/clear" >/dev/null 2>&1; then
    echo "✅ Content cache cleared via API"
else
    echo "⚠️  API cache clear failed, restarting app container..."
    $DOCKER_COMPOSE -f docker-compose.prod.yml --env-file .env.prod restart app
    
    # Wait for app to restart
    echo "⏳ Waiting for application to restart..."
    sleep 15
    
    # Verify app is healthy
    RETRY_COUNT=0
    MAX_RETRIES=6
    while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
        if curl -f --max-time 10 "$API_URL/health" >/dev/null 2>&1; then
            echo "✅ Application restarted successfully"
            break
        else
            echo "⏳ Waiting for application... (attempt $((RETRY_COUNT + 1))/$MAX_RETRIES)"
            sleep 5
            RETRY_COUNT=$((RETRY_COUNT + 1))
        fi
    done
    
    if [ $RETRY_COUNT -eq $MAX_RETRIES ]; then
        echo "❌ Application restart verification failed"
        exit 1
    fi
fi

# Verify content is accessible
echo "🧪 Verifying content deployment..."

# Test changelog
if curl -f --max-time 10 "$API_URL/api/public/changelog" | grep -q '"success":true' 2>/dev/null; then
    echo "✅ Changelog content is accessible"
else
    echo "❌ Changelog content verification failed"
    exit 1
fi

# Test help articles
if curl -f --max-time 10 "$API_URL/api/public/help" | grep -q '"success":true' 2>/dev/null; then
    echo "✅ Help content is accessible"
else
    echo "❌ Help content verification failed"
    exit 1
fi

# Test static pages
if curl -f --max-time 10 "$API_URL/api/public/static" | grep -q '"success":true' 2>/dev/null; then
    echo "✅ Static pages content is accessible"
else
    echo "❌ Static pages content verification failed"
    exit 1
fi

echo ""
echo "✅ Content deployment completed successfully!"
echo "📝 New content is live without full application rebuild"
echo ""
echo "🌐 You can view the updated content at:"
echo "   • Changelog: https://emailconnect.eu/changelog"
echo "   • Help: https://emailconnect.eu/help"
echo "   • Privacy Policy: https://emailconnect.eu/privacy-policy"
echo "   • Terms of Service: https://emailconnect.eu/terms-of-service"