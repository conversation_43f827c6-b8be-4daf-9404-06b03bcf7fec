# Changelog and Help System Implementation Recommendations

## Overview

This document provides recommendations for implementing `/changelog` and `/help` endpoints that can be easily managed without requiring full deployments for content updates.

## Requirements Analysis

Based on the user's requirements:

1. **Changelog Requirements:**
   - Index list with all entries
   - Each entry shows: date, type (fix/added/changed), short description
   - Easy content management without full deployments

2. **Help Requirements:**
   - Index list showing title and excerpt
   - Links to full pages (e.g., `/help/how-to-do-x`)
   - Easy content management without full deployments

## Recommended Approaches

### Option 1: File-Based with Git Integration (Recommended for MVP)

**Implementation:**
- Store markdown files in `/content` directory
- Use simple file-based API to read and parse markdown
- Git-based workflow for content updates

**Pros:**
- Simple to implement
- Version controlled content
- No additional database complexity
- Familiar markdown workflow
- Can be enhanced later

**Cons:**
- Requires deployment for updates
- No web-based editing interface

**Structure:**
```
content/
├── changelog/
│   ├── index.md
│   ├── 2024-06-24-user-settings.md
│   ├── 2024-06-23-domain-fixes.md
│   └── ...
└── help/
    ├── index.md
    ├── getting-started.md
    ├── domain-setup.md
    └── ...
```

**Implementation Details:**
- Use frontmatter for metadata (date, type, title, excerpt)
- Simple markdown parser (e.g., `marked` or `markdown-it`)
- Cache parsed content in memory
- Watch for file changes in development

### Option 2: Database-Driven CMS

**Implementation:**
- Create `Article` and `ChangelogEntry` models
- Build admin interface for content management
- Rich text editor or markdown editor

**Pros:**
- No deployment needed for updates
- Web-based content management
- User-friendly editing interface
- Advanced features (drafts, scheduling, etc.)

**Cons:**
- More complex to implement
- Requires admin UI development
- Additional database models
- More maintenance overhead

### Option 3: Hybrid Approach

**Implementation:**
- Start with file-based system
- Add database caching layer
- Optional web editing that writes to files
- Git integration for version control

**Pros:**
- Best of both worlds
- Can evolve incrementally
- Maintains version control
- Allows web editing

**Cons:**
- Most complex to implement
- Potential sync issues between files and database

### Option 4: External Headless CMS

**Implementation:**
- Use service like Strapi, Contentful, or Sanity
- API integration for content retrieval
- External content management interface

**Pros:**
- Professional content management features
- No development of admin interface needed
- Advanced features out of the box

**Cons:**
- Additional service dependency
- Potential costs
- External service reliability
- API rate limits

## Detailed Recommendation: Option 1 (File-Based)

### Implementation Plan

#### 1. Content Structure

**Changelog Entry Format:**
```markdown
---
date: 2024-06-24
type: added
title: User Settings for Storage Configuration
summary: Added user settings model and API for attachment processing configuration
---

# User Settings for Storage Configuration

Added comprehensive user settings system for managing attachment processing and storage configuration...
```

**Help Article Format:**
```markdown
---
title: Setting Up Your First Domain
slug: setting-up-your-first-domain
excerpt: Learn how to add and verify your domain for email processing
tag: getting-started
order: 1
---

# Setting Up Your First Domain

This guide will walk you through...
```

#### 2. API Endpoints

**Changelog Endpoints:**
- `GET /api/public/changelog` - List all changelog entries, no need for deep-links. It's just the 1-page.

**Help Endpoints:**
- `GET /api/public/help` - List all help articles
- `GET /api/public/help/:slug` - Get specific help article

#### 3. Frontend Routes

**Changelog Routes:**
- `/changelog` - Changelog index page

**Help Routes:**
- `/help` - Help index page
- `/help/:slug` - Individual help article

#### 4. Content Service Implementation

```typescript
// content.service.ts
export class ContentService {
  private contentCache = new Map();
  
  async getChangelogEntries(): Promise<ChangelogEntry[]> {
    // Read markdown files from content/changelog/
    // Parse frontmatter and content
    // Sort by date descending
    // Return structured data
  }
  
  async getHelpArticles(): Promise<HelpArticle[]> {
    // Read markdown files from content/help/
    // Parse frontmatter and content
    // Sort by category and order
    // Return structured data
  }
}
```

### Migration Path

1. **Phase 1: Basic File-Based System**
   - Implement content service
   - Create API endpoints
   - Build frontend pages
   - Add initial content

2. **Phase 2: Enhanced Features**
   - Add search functionality
   - Implement content caching
   - Add RSS feed for changelog
   - Improve SEO optimization

3. **Phase 3: Optional Web Interface**
   - Add admin interface for content editing
   - Implement file writing capabilities
   - Add preview functionality
   - Maintain Git integration

### Content Management Workflow

1. **Adding Changelog Entry:**
   ```bash
   # Create new file
   touch content/changelog/2024-06-24-new-feature.md
   
   # Edit content with frontmatter
   # Commit and push
   git add content/changelog/2024-06-24-new-feature.md
   git commit -m "Add changelog entry for new feature"
   git push
   
   # Deploy (if needed for file updates)
   ```

2. **Adding Help Article:**
   ```bash
   # Create new file
   touch content/help/advanced-webhooks.md
   
   # Edit content with frontmatter
   # Commit and push
   git add content/help/advanced-webhooks.md
   git commit -m "Add help article for advanced webhooks"
   git push
   ```

## Alternative: Quick Win with Static Files

For immediate implementation, you could:

1. Create static JSON files for changelog and help data
2. Serve them via API endpoints
3. Update JSON files for new content
4. Migrate to full markdown system later

**Example:**
```json
// content/changelog.json
{
  "entries": [
    {
      "date": "2024-06-24",
      "type": "added",
      "title": "User Settings",
      "excerpt": "Added user settings for storage configuration",
      "slug": "user-settings-storage"
    }
  ]
}
```

## Conclusion

**Recommended Approach:** Start with Option 1 (File-Based) for the following reasons:

1. **Quick to implement** - Can be done in a few hours
2. **Familiar workflow** - Markdown + Git is developer-friendly
3. **Version controlled** - All content changes are tracked
4. **Scalable** - Can be enhanced with caching, search, etc.
5. **No vendor lock-in** - Pure markdown files are portable

**Next Steps:**
1. Implement basic file-based content service
2. Create API endpoints for changelog and help
3. Build frontend pages
4. Add initial content
5. Consider web-based editing interface for future enhancement

This approach provides immediate value while maintaining flexibility for future enhancements.

## Sample Implementation

### Basic Content Service

```typescript
// src/backend/services/content.service.ts
import fs from 'fs/promises';
import path from 'path';
import matter from 'gray-matter';

export interface ChangelogEntry {
  slug: string;
  date: string;
  type: 'added' | 'changed' | 'fixed';
  title: string;
  excerpt: string;
  content: string;
}

export interface HelpArticle {
  slug: string;
  title: string;
  excerpt: string;
  category: string;
  order: number;
  content: string;
}

export class ContentService {
  private contentDir = path.join(process.cwd(), 'content');

  async getChangelogEntries(): Promise<ChangelogEntry[]> {
    const changelogDir = path.join(this.contentDir, 'changelog');
    const files = await fs.readdir(changelogDir);

    const entries = await Promise.all(
      files
        .filter(file => file.endsWith('.md'))
        .map(async (file) => {
          const filePath = path.join(changelogDir, file);
          const fileContent = await fs.readFile(filePath, 'utf-8');
          const { data, content } = matter(fileContent);

          return {
            slug: file.replace('.md', ''),
            date: data.date,
            type: data.type,
            title: data.title,
            excerpt: data.excerpt,
            content
          };
        })
    );

    return entries.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  }

  async getHelpArticles(): Promise<HelpArticle[]> {
    const helpDir = path.join(this.contentDir, 'help');
    const files = await fs.readdir(helpDir);

    const articles = await Promise.all(
      files
        .filter(file => file.endsWith('.md'))
        .map(async (file) => {
          const filePath = path.join(helpDir, file);
          const fileContent = await fs.readFile(filePath, 'utf-8');
          const { data, content } = matter(fileContent);

          return {
            slug: file.replace('.md', ''),
            title: data.title,
            excerpt: data.excerpt,
            category: data.category || 'general',
            order: data.order || 999,
            content
          };
        })
    );

    return articles.sort((a, b) => a.order - b.order);
  }
}
```

### API Routes

```typescript
// src/backend/routes/content.routes.ts
import { FastifyPluginAsync } from 'fastify';
import { ContentService } from '../services/content.service.js';

const contentService = new ContentService();

export const contentRoutes: FastifyPluginAsync = async (fastify) => {
  // Changelog endpoints
  fastify.get('/changelog', async () => {
    const entries = await contentService.getChangelogEntries();
    return { success: true, entries };
  });

  fastify.get('/changelog/:slug', async (request) => {
    const { slug } = request.params as { slug: string };
    const entries = await contentService.getChangelogEntries();
    const entry = entries.find(e => e.slug === slug);

    if (!entry) {
      throw new Error('Changelog entry not found');
    }

    return { success: true, entry };
  });

  // Help endpoints
  fastify.get('/help', async () => {
    const articles = await contentService.getHelpArticles();
    return { success: true, articles };
  });

  fastify.get('/help/:slug', async (request) => {
    const { slug } = request.params as { slug: string };
    const articles = await contentService.getHelpArticles();
    const article = articles.find(a => a.slug === slug);

    if (!article) {
      throw new Error('Help article not found');
    }

    return { success: true, article };
  });
};
```

This implementation provides a solid foundation that can be enhanced incrementally.
